<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura;

use App\Models\FaturaGsnet;
use App\Models\FaturaGsnetItem;
use Domain\MinisterioSaude\Exceptions\CustomMessageException;
use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\AtualizarStatusFaturaInput;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput;

class FaturaGsnetService
{
    use HasServiceResponse;
    use HasLog;

    private Client $client;
    private string $baseUrl;
    private string $accessToken;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
        $this->accessToken = config('ministerio_saude.api.access_token');
    }
    /**
     * API 2.1 - Consultar Faturas via API real do Ministério
     *
     * @param ConsultaFaturaInput $data
     * @return array
     */
    public function consultarFaturas(ConsultaFaturaInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_faturas_gsnet');
            $queryParams = $input->toQueryParams($this->accessToken);
            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);

            $this->logInfo('FaturaGsnetService - Consultando faturas via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null,
                'total_faturas' => count($responseBody['Data'] ?? [])
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na consulta', $responseBody);
            }

            return $this->successResponse($responseBody, 'Consulta de faturas realizada com sucesso via API real do Ministério.');
        } catch (CustomMessageException $e) {
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";

            $this->logError('FaturaGsnetService - Erro na requisição para o Ministério', $e, [
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $input->toArray()
            ]);

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (\Exception $e) {
            $this->logError('FaturaGsnetService - Erro geral', $e, [
                'request_data' => $input->toArray()
            ]);

            $response = $this->failureResponse('Erro interno: ' . $e->getMessage())->setNullData();
        }
        return $response;
    }

    /**
     * API 2.2 - Atualizar Status da Fatura
     *
     * @param AtualizarStatusFaturaInput $input
     * @return ServiceResponse
     */
    public function atualizarStatusFatura(AtualizarStatusFaturaInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.atualizar_status_fatura_gsnet');
            $queryParams = $input->toQueryParams($this->accessToken);
            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);
            $this->logInfo('FaturaGsnetService - Atualizando status da fatura via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);
            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            $responseBody = json_decode($response->getBody()->getContents(), true);
            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null
            ]);
            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na atualização', $responseBody);
            }
            return $this->successResponse($responseBody, 'Status da fatura atualizado com sucesso');
        } catch (CustomMessageException $e) {
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
            $this->logError('FaturaGsnetService - Erro na atualização do status da fatura', $e, [
                'url' => $url ?? 'URL não definida',
                'data' => $queryParams ?? []
            ]);

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (\Exception $e) {
            $this->logError('FaturaGsnetService - Erro ao atualizar status da fatura', $e);
            $response =  $this->failureResponse('Erro ao atualizar status da fatura: ' . $e->getMessage())->setNullData();
        }
        return $response;
    }

    /**
     * Buscar fatura por protocolo
     *
     * @param string $protocoloId
     * @return FaturaGsnet|null
     */
    public function buscarPorProtocolo(string $protocoloId): ?FaturaGsnet
    {
        return FaturaGsnet::with(['itens', 'statusControle'])
            ->where('protocolo_id_gsnet', $protocoloId)
            ->where('ativo', true)
            ->first();
    }

    /**
     * Criar nova fatura
     *
     * @param array $dadosFatura
     * @param array $itens
     * @return FaturaGsnet
     */
    public function criarFatura(array $dadosFatura, array $itens = []): FaturaGsnet
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            $fatura = FaturaGsnet::create($dadosFatura);

            // Criar itens se fornecidos
            foreach ($itens as $item) {
                $item['fatura_id'] = $fatura->id;
                $item['protocolo_id_gsnet'] = $fatura->protocolo_id_gsnet;
                FaturaGsnetItem::create($item);
            }

            DB::connection('ministerio_saude_sp')->commit();

            return $fatura->load(['itens', 'statusControle']);

        } catch (\Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            throw $e;
        }
    }
}
