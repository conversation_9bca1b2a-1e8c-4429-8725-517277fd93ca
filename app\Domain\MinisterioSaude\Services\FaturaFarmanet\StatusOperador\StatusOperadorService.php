<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador;

use Domain\MinisterioSaude\Exceptions\CustomMessageException;
use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\CriarStatusInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\ConsultarStatusInput;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
use Domain\MinisterioSaude\Exceptions\DuplicateRecordException;

class StatusOperadorService
{
    use HasServiceResponse;
    use HasLog;

    private $client;
    private $baseUrl;
    private $accessToken;
    private StatusOperadorRepositoryInterface $repository;
    public function __construct(StatusOperadorRepositoryInterface $repository)
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.farmanet.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
        $this->repository = $repository;
        $this->accessToken = config('ministerio_saude.api.access_token');
    }

    /**
     * Criar novo status tanto localmente quanto na API
     */
    public function criarStatus(CriarStatusInput $data): ServiceResponse
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            $statusExistente = $this->repository->getOneWhere(
                fn ($query) =>
                $query->where('id_origem', $data->idOrigem)
                    ->orWhere('nome_status', $data->nomeStatus)
            );

            if ($statusExistente) {
                DB::connection('ministerio_saude_sp')->rollBack();

                $duplicatedFields = $this->getDuplicatedFields($statusExistente, $data);
                $message = $this->buildDuplicationMessage($duplicatedFields);

                throw new DuplicateRecordException($message, $duplicatedFields, 422);
            }

            // Fazer chamada HTTP real para a API do Ministério
            try {
                $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.inserir_status_op');

                $requestData = $data->toQueryParams($this->accessToken);

                $this->logInfo('StatusOperadorService - Chamando API real do Ministério', [
                    'url' => $url,
                    'data' => collect($requestData)->except(['AccessToken'])->toArray()
                ]);

                $response = $this->client->post($url, ['json' => $requestData]);
                $responseBody = json_decode($response->getBody()->getContents(), true);

                $this->logInfo('StatusOperadorService - Resposta da API real recebida', [
                    'status_code' => $response->getStatusCode(),
                    'response' => $responseBody
                ]);

            } catch (RequestException $e) {
                DB::connection('ministerio_saude_sp')->rollBack();

                $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
                $this->logError('StatusOperadorService - Erro na API externa', $e, [
                    'url' => $url ?? 'URL não definida',
                    'data' => $requestData ?? []
                ]);

                return $this->failureResponse($errorMessage)->setNullData();
            }

            $status = $this->repository->store([
                'id_origem' => $data->idOrigem,
                'nome_status' => $data->nomeStatus,
                'descricao_status' => $data->descricaoStatus,
                'flag_registro' => true
            ]);

            DB::connection('ministerio_saude_sp')->commit();

            $this->logInfo('StatusOperadorService - Status criado com sucesso', [
                'status_id' => $status->get('id'),
                'id_origem' => $data->idOrigem,
                'nome_status' => $data->nomeStatus,
                'api_externa_usada' => true
            ]);

            return $this->successResponse($status, 'Status incluído com sucesso');
        } catch (Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            $this->logError('StatusOperadorService - Erro ao criar status', $e);
            return $this->failureResponse("Erro interno ao criar status: {$e->getMessage()}")->setNullData();
        }
    }

    /**
     * Consultar status via API real do Ministério
     */
    public function consultarStatus(ConsultarStatusInput $input): ServiceResponse
    {
        try {

            $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.consultar_status_op');

            $queryParams = ['AccessToken' => $this->accessToken];
            // Adicionar parâmetros não nulos
            if ($input->idStatus !== null) {
                $queryParams['IdStatus'] = $input->idStatus;
            }
            if ($input->idOrigem !== null) {
                $queryParams['IdOrigem'] = $input->idOrigem;
            }
            if ($input->nomeStatus !== null) {
                $queryParams['NomeStatus'] = $input->nomeStatus;
            }

            $this->logInfo('StatusOperadorService - Consultando status via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            $response = $this->client->get($url, [
                'query' => $queryParams,
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            $responseBody = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('StatusOperadorService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'total_records' => count($responseBody['Data'] ?? [])
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na consulta', $responseBody);
            }

            return $this->successResponse($responseBody, 'Consulta realizada com sucesso');
        } catch (CustomMessageException $e) {
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
            $this->logError('StatusOperadorService - Erro na consulta da API externa', $e, ['url' => $url ?? 'URL não definida']);

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (Exception $e) {
            $this->logError('StatusOperadorService - Erro ao consultar status', $e);

            $response = $this->failureResponse("Erro interno ao consultar status: {$e->getMessage()}")->setNullData();
        }
        return $response;
    }

    private function getDuplicatedFields($statusExistente, $data): array
    {
        $duplicatedFields = [];

        $fieldMappings = [
            'id_origem' => ['field' => 'idOrigem', 'label' => 'Id de Origem'],
            'nome_status' => ['field' => 'nomeStatus', 'label' => 'Nome do Status']
        ];

        foreach ($fieldMappings as $dbField => $mapping) {
            if ($statusExistente->{$dbField} == $data->{$mapping['field']}) {
                $duplicatedFields[] = $mapping['label'];
            }
        }

        return $duplicatedFields;
    }

    private function buildDuplicationMessage(array $duplicatedFields): string
    {
        if (empty($duplicatedFields)) {
            return 'Registro já existe na base';
        }

        if (count($duplicatedFields) === 1) {
            return "{$duplicatedFields[0]} já consta na base";
        }

        return implode(' e ', $duplicatedFields) . ' já constam na base';
    }
}
