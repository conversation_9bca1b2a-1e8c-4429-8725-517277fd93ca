<?php

namespace Domain\MinisterioSaude\Services\Item;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Services\Item\Input\ConsultarItensInput;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Carbon\Carbon;

class ConsultarItensService
{
    use HasServiceResponse;
    use HasLog;

    private $client;
    private $baseUrl;
    private $accessToken;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.items.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.items.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
        $this->accessToken = config('ministerio_saude.api.access_token');
    }

    /**
     * Consulta itens no sistema GSNET do Ministério da Saúde
     *
     * @param ConsultarItensInput $input
     * @return ServiceResponse
     */
    public function consultarItens(ConsultarItensInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.items.endpoints.consultar_itens');
            $url = "{$this->baseUrl}{$endpoint}";

            $this->logInfo('ConsultarItensService - Iniciando consulta de itens via API real', [
                'url' => $url,
                'total_itens' => count($input->listaItens),
                'codigos_materiais' => array_column($input->listaItens, 'CodigoMaterial')
            ]);

            $payload = $input->toQueryParams($this->accessToken);
            $response = $this->client->put($url, ['json' => $payload]);
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('ConsultarItensService - Resposta recebida do Ministério', [
                'status_code' => $response->getStatusCode(),
                'total_itens_retornados' => count($responseData['Data'] ?? []),
                'protocolo' => $responseData['Protocolo'] ?? null
            ]);

            return $this->successResponse([
                'result' => $responseData,
                'total_itens' => count($responseData['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ], 'Consulta de itens realizada com sucesso via API real do Ministério.');

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();

            $this->logError('ConsultarItensService - Erro na requisição para o Ministério', $e, [
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $input
            ]);

            return $this->failureResponse($errorMessage)->setNullData();

        } catch (\Exception $e) {
            $this->logError('ConsultarItensService - Erro geral', $e, ['request_data' => $input]);

            return $this->failureResponse("Erro ao consultar itens: {$e->getMessage()}")
                ->setNullData()
                ->setErrorCode('INTERNAL_ERROR');
        }
    }
}
