<?php

use Domain\MinisterioSaude\Controllers\PlanejamentoController;
use Illuminate\Support\Facades\Route;
use Domain\MinisterioSaude\Controllers\FaturaFarmanetController;
use Domain\MinisterioSaude\Controllers\ConsultarPedidosFarmanetController;
use Domain\MinisterioSaude\Controllers\AtualizarPedidoFarmanetController;
use Domain\MinisterioSaude\Controllers\ItemController;
use Domain\MinisterioSaude\Controllers\RecebimentoFaturasFarmanetController;
use Domain\MinisterioSaude\Controllers\ObterPrecoMedioItensController;

/*
|--------------------------------------------------------------------------
| Ministério da Saúde SP Routes - Completa
|--------------------------------------------------------------------------
|
| APIs para integração com o Ministério da Saúde SP.
| Inclui todas as APIs do sistema: MVPs e Farmanet.
|
*/

Route::prefix('ministerio-saude/sp')->group(function () {

    // === APIs PRINCIPAIS (MVP) ===

    // API 1.1 - Inserir Status na Fatura
    Route::post('/status/criar', [FaturaFarmanetController::class, 'inserirStatusFatura'])
        ->name('ministerio-saude.inserir-status-fatura');

    // API 1.2 - Consultar Status do Operador
    Route::get('/status/consultar', [FaturaFarmanetController::class, 'consultarStatusOp'])
        ->name('ministerio-saude.consultar-status-op');

    // API 1.3 - Consultar Endereço Local
    Route::get('/endereco/consultar', [FaturaFarmanetController::class, 'consultarEndereco'])
        ->name('ministerio-saude.consultar-endereco');

    // API 1.4 - Consultar Itens (Recebe)
    Route::post('/itens/consultar', [ItemController::class, 'consultarItens'])
        ->name('ministerio-saude.consultar-itens');

    // === NOVAS APIs - FATURAS (TESTE SEM AUTH) ===

    // API 2.1 - Consultar Faturas (Recebe) - TESTE
    Route::get('/faturas/consultar-teste', [FaturaFarmanetController::class, 'consultarFaturas'])
        ->name('ministerio-saude.consultar-faturas-teste');

    // API 2.2 - Atualizar Status da Fatura (Envio) - TESTE
    Route::post('/faturas/status/atualizar-teste', [FaturaFarmanetController::class, 'atualizarStatusFatura'])
        ->name('ministerio-saude.atualizar-status-fatura-teste');

    // === NOVAS APIs - FATURAS ===

    // API 2.1 - Consultar Faturas (Recebe)
    Route::get('/faturas/consultar', [FaturaFarmanetController::class, 'consultarFaturas'])
        ->name('ministerio-saude.consultar-faturas');

    // API 2.2 - Atualizar Status da Fatura (Envio)
    Route::put('/faturas/status/atualizar', [FaturaFarmanetController::class, 'atualizarStatusFatura'])
        ->name('ministerio-saude.atualizar-status-fatura');

    // === NOVAS APIs - FARMANET (3.1 - 3.4) ===

    // API 3.1 - Consultar Pedidos Farmanet (Recebe)
    Route::get('/pedidos-farmanet/consultar', [PlanejamentoController::class, 'consultarPedidos'])
        ->name('ministerio-saude.consultar-pedidos-farmanet');

    Route::get('/pedidos-farmanet/locais', [ConsultarPedidosFarmanetController::class, 'buscarPedidosLocais'])
        ->name('ministerio-saude.buscar-pedidos-farmanet-locais');

    Route::get('/pedidos-farmanet/info', [ConsultarPedidosFarmanetController::class, 'info'])
        ->name('ministerio-saude.pedidos-farmanet-info');

    // API 3.2 - Atualizar Pedidos Farmanet (Envio)
    Route::put('/pedidos-farmanet/atualizar', [PlanejamentoController::class, 'atualizarPedido'])
        ->name('ministerio-saude.atualizar-pedido-farmanet');

    Route::post('/pedidos-farmanet/validar-atualizacao', [AtualizarPedidoFarmanetController::class, 'validarAtualizacao'])
        ->name('ministerio-saude.validar-atualizacao-pedido-farmanet');

    Route::get('/pedidos-farmanet/status', [AtualizarPedidoFarmanetController::class, 'listarStatus'])
        ->name('ministerio-saude.listar-status-pedidos-farmanet');

    Route::get('/pedidos-farmanet/atualizar/info', [AtualizarPedidoFarmanetController::class, 'info'])
        ->name('ministerio-saude.atualizar-pedido-farmanet-info');

    // API 3.3 - Recebimento Faturas Farmanet (Recebe)
    Route::post('/faturas-farmanet/consultar', [RecebimentoFaturasFarmanetController::class, 'consultarFaturas'])
        ->name('ministerio-saude.consultar-faturas-farmanet');

    Route::post('/faturas-farmanet/confirmar-recebimento', [RecebimentoFaturasFarmanetController::class, 'confirmarRecebimento'])
        ->name('ministerio-saude.confirmar-recebimento-fatura-farmanet');

    Route::get('/faturas-farmanet/locais', [RecebimentoFaturasFarmanetController::class, 'buscarFaturasLocais'])
        ->name('ministerio-saude.buscar-faturas-farmanet-locais');

    Route::get('/faturas-farmanet/status', [RecebimentoFaturasFarmanetController::class, 'listarStatus'])
        ->name('ministerio-saude.listar-status-faturas-farmanet');

    Route::get('/faturas-farmanet/info', [RecebimentoFaturasFarmanetController::class, 'info'])
        ->name('ministerio-saude.faturas-farmanet-info');

    // API 3.4 - Obter Preço Médio de Itens (Recebe)
    Route::post('/preco-medio-itens/obter', [ObterPrecoMedioItensController::class, 'obterPrecoMedio'])
        ->name('ministerio-saude.obter-preco-medio-itens');

    Route::get('/preco-medio-itens/locais', [ObterPrecoMedioItensController::class, 'buscarPrecosLocais'])
        ->name('ministerio-saude.buscar-precos-locais');

    Route::post('/preco-medio-itens/estatisticas', [ObterPrecoMedioItensController::class, 'obterEstatisticas'])
        ->name('ministerio-saude.obter-estatisticas-precos');

    Route::post('/preco-medio-itens/comparar', [ObterPrecoMedioItensController::class, 'compararPrecos'])
        ->name('ministerio-saude.comparar-precos');

    Route::get('/preco-medio-itens/programas', [ObterPrecoMedioItensController::class, 'listarProgramas'])
        ->name('ministerio-saude.listar-programas-precos');

    Route::get('/preco-medio-itens/info', [ObterPrecoMedioItensController::class, 'info'])
        ->name('ministerio-saude.preco-medio-itens-info');

});
