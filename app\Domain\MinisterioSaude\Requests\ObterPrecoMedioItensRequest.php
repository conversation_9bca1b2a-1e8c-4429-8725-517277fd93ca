<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ObterPrecoMedioItensRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'codigo_programa' => 'required|integer|in:5,24,25,28',
            'ano_referencia' => 'required|integer|digits:4|min:2020|max:2030',
            'mes_referencia' => 'nullable|integer|min:1|max:12',
            'codigo_medicamento' => 'nullable|string|max:20',
            'codigo_farmaco' => 'nullable|string|max:20',
            'estado_origem' => 'nullable|string|size:2',
            'access_token' => 'required|string|max:40',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'codigo_programa.required' => 'O código do programa é obrigatório',
            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
            'codigo_programa.in' => 'O código do programa deve ser 5 (Diabetes), 24 (Dose Certa), 25 (Saúde da Mulher) ou 28 (Arboviroses)',
            
            'ano_referencia.required' => 'O ano de referência é obrigatório',
            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
            'ano_referencia.digits' => 'O ano de referência deve ter 4 dígitos',
            'ano_referencia.min' => 'O ano de referência deve ser maior que 2019',
            'ano_referencia.max' => 'O ano de referência deve ser menor que 2031',
            
            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
            'mes_referencia.min' => 'O mês de referência deve ser entre 1 e 12',
            'mes_referencia.max' => 'O mês de referência deve ser entre 1 e 12',
            
            'codigo_medicamento.string' => 'O código do medicamento deve ser uma string',
            'codigo_medicamento.max' => 'O código do medicamento não pode ter mais de 20 caracteres',
            
            'codigo_farmaco.string' => 'O código do fármaco deve ser uma string',
            'codigo_farmaco.max' => 'O código do fármaco não pode ter mais de 20 caracteres',
            
            'estado_origem.string' => 'O estado de origem deve ser uma string',
            'estado_origem.size' => 'O estado de origem deve ter exatamente 2 caracteres',
            
            'access_token.required' => 'O token de acesso é obrigatório',
            'access_token.string' => 'O token de acesso deve ser uma string',
            'access_token.max' => 'O token de acesso não pode ter mais de 40 caracteres',
        ];
    }
}
