<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal;

use Domain\MinisterioSaude\DTOs\EnderecoResponseDTO;
use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
use Domain\MinisterioSaude\Repositories\EnderecoLocalRepositoryInterface;
use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input\ConsultaEnderecoInput;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use Domain\MinisterioSaude\Helpers\ServiceResponse;
use InvalidArgumentException;
use Domain\MinisterioSaude\Services\GeolocalizacaoService;
use Domain\MinisterioSaude\Exceptions\CustomMessageException;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;

class EnderecoLocalService
{
    use HasServiceResponse;
    use HasLog;

    private EnderecoLocalRepositoryInterface $repository;
    private GeolocalizacaoService $geoService;
    private Client $client;
    private string $baseUrl;
    private ?string $accessToken;
    private MinisterioSaudeLogRepositoryInterface $logRepository;

    public function __construct(
        EnderecoLocalRepositoryInterface $repository,
        GeolocalizacaoService $geoService,
        MinisterioSaudeLogRepositoryInterface $logRepository
    ) {
        $this->repository = $repository;
        $this->geoService = $geoService;
        $this->logRepository = $logRepository;

        $this->accessToken = config('ministerio_saude.api.access_token');
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
    }

    /**
     * Consultar endereço na API do Ministério da Saúde
     */
    public function consultarEndereco(ConsultaEnderecoInput $input): ServiceResponse
    {
        if ($this->accessToken === null) {
            throw new InvalidArgumentException('Missing API configuration');
        }

        try {
            $startTime = microtime(true);

            if (!$input->isValid()) {
                throw new CustomMessageException('Dados de entrada inválidos: ' . implode(', ', $input->getErrors()), null, "INVALID_INPUT", 400);
            }

            $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.consultar_endereco_local');
            $queryParams = $input->toQueryParams($this->accessToken);

            $this->LogInfo('EnderecoLocalService - Chamando API real do Ministério', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            $response = $this->client->get($url, [
                'query' => $queryParams,
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);
            $endTime = microtime(true);

            $tempoResposta = round(($endTime - $startTime) * 1000);

            if ($response->getStatusCode() === 200 && !empty($responseBody['Data'])) {
                $enderecoDTO = new EnderecoResponseDTO($responseBody['Data'][0] ?? []);

                $this->LogInfo('EnderecoLocalService - Resposta da API real recebida', [
                    'status_code' => $response->getStatusCode(),
                    'endereco_encontrado' => !empty($responseBody['Data'][0])
                ]);

                // Log de sucesso
                $this->logRepository->storeConsultaEndereco(
                    $queryParams,
                    array_merge($responseBody, ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
                    true
                );

                return $this->successResponse([
                    'endereco_api' => $enderecoDTO->toArray(),
                    'tempo_resposta_ms' => $tempoResposta,
                    'fonte' => 'API Real do Ministério da Saúde'
                ], 'Endereço consultado com sucesso via API real');
            }

            // Log de erro
            $this->logRepository->storeConsultaEndereco(
                $queryParams,
                array_merge($responseBody ?? [], ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
                false,
                $responseBody['Message'] ?? 'Endereço não encontrado'
            );

            throw new CustomMessageException($responseBody['Message'] ?? 'Endereço não encontrado na API', $responseBody, "NOT_FOUND", 404);
        } catch (CustomMessageException $e) {

            $this->logError('EnderecoLocalService@consultarEndereco - Erro', $e, [
                'id_gestor' => $input->idGestor,
                'id_local' => $input->idLocal
            ]);

            $response = $this->failureResponse($e->getMessage(), $e->getData());
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;

            if ($statusCode === 404) {
                $this->logWarning('EnderecoLocalService - API de endereço não encontrada', [
                    'url' => $url,
                    'status_code' => $statusCode,
                    'message' => 'Endpoint de endereço não está disponível na API do Ministério'
                ]);

                return $this->notFoundResponse("API de consulta de endereços não está disponível no Ministério da Saúde SP. Endpoint: {$url}");
            }

            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            $this->logError('EnderecoLocalService - Erro na API externa', $e, [
                'url' => $url ?? 'URL não definida'
            ]);

            $response = $this->failureResponse($errorMessage)->setErrorCode("FORBIDDEN");

        } catch (\Exception $e) {
            $this->logError('EnderecoLocalService@consultarEndereco - Erro', $e, [
                'id_gestor' => $input->idGestor,
                'id_local' => $input->idLocal
            ]);

            // Log de erro
            $this->logRepository->storeConsultaEndereco(
                ['IdGestor' => $input->idGestor, 'IdLocal' => $input->idLocal],
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse('Erro interno do servidor', null)->setNullData();
        }
        return $response;
    }

    /**
     * Listar endereços locais
     */
    public function listarEnderecos(): array
    {
        try {
            $enderecos = $this->repository->findAtivos();

            return [
                'success' => true,
                'message' => 'Endereços listados com sucesso',
                'data' => $enderecos->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@listarEnderecos - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Obter endereço por ID
     */
    public function obterEndereco(string $idGestor, string $idLocal): array
    {
        try {
            $endereco = $this->repository->findByGestorELocal($idGestor, $idLocal);

            if (!$endereco) {
                return [
                    'success' => false,
                    'message' => 'Endereço não encontrado',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => 'Endereço encontrado',
                'data' => $endereco->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@obterEndereco - Erro', [
                'id_gestor' => $idGestor,
                'id_local' => $idLocal,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Sincronizar endereço específico com a API
     */
    public function sincronizarEndereco(string $idGestor, string $idLocal): array
    {
        try {
            $resultado = $this->consultarEndereco($idGestor, $idLocal);

            if ($resultado['success']) {
                return [
                    'success' => true,
                    'message' => 'Endereço sincronizado com sucesso',
                    'data' => $resultado['data']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Falha na sincronização: ' . $resultado['message'],
                    'data' => $resultado['data']
                ];
            }

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@sincronizarEndereco - Erro', [
                'id_gestor' => $idGestor,
                'id_local' => $idLocal,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Processar geolocalização para endereços sem coordenadas
     */
    public function processarGeolocalizacaoLote(int $limite = null): array
    {
        try {
            $limite = $limite ?? config('ministerio_saude.geolocalizacao.batch_limit', 50);

            if (!config('ministerio_saude.geolocalizacao.enabled')) {
                return [
                    'success' => false,
                    'message' => 'Geolocalização está desabilitada',
                    'data' => null
                ];
            }

            $resultados = $this->geoService->processarLoteGeolocalizacao($limite);

            return [
                'success' => true,
                'message' => 'Processamento de geolocalização concluído',
                'data' => $resultados
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@processarGeolocalizacaoLote - Erro', [
                'limite' => $limite,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }

    /**
     * Obter estatísticas dos endereços
     */
    public function obterEstatisticas(): array
    {
        try {
            $total = $this->repository->findAll()->count();
            $ativos = $this->repository->findAtivos()->count();
            $semGeolocalizacao = $this->repository->findSemGeolocalizacao()->count();
            $comGeolocalizacao = $ativos - $semGeolocalizacao;

            return [
                'success' => true,
                'message' => 'Estatísticas obtidas com sucesso',
                'data' => [
                    'total_enderecos' => $total,
                    'enderecos_ativos' => $ativos,
                    'com_geolocalizacao' => $comGeolocalizacao,
                    'sem_geolocalizacao' => $semGeolocalizacao,
                    'percentual_geolocalizados' => $ativos > 0 ? round(($comGeolocalizacao / $ativos) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('EnderecoLocalService@obterEstatisticas - Erro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor',
                'data' => null
            ];
        }
    }
}
