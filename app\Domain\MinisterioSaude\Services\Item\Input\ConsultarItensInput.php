<?php

namespace Domain\MinisterioSaude\Services\Item\Input;

class ConsultarItensInput
{
    /** @var ItemMaterial[] */
    public $listaItens;

    /**
     * @param ItemMaterial[] $listaItens
     */
    public function __construct(array $listaItens)
    {
        $this->listaItens = $listaItens;
    }

    public static function fromArray(array $data): ConsultarItensInput
    {
        $listaItens = [];

        if (isset($data['lista_itens']) && is_array($data['lista_itens'])) {
            foreach ($data['lista_itens'] as $item) {
                if (isset($item['codigo_material'])) {
                    $listaItens[] = ItemMaterial::fromArray($item);
                }
            }
        }

        return new self($listaItens);
    }

    public function toQueryParams(string $accessToken): array
    {
        $listaItensArray = [];
        foreach ($this->listaItens as $item) {
            $listaItensArray[] = $item->toArray();
        }

        return [
            'Data' => [
                'ListaItens' => $listaItensArray
            ],
            'AccessToken' => $accessToken
        ];
    }
}

class ItemMaterial
{
    public int $codigoMaterial;

    public function __construct(int $codigoMaterial)
    {
        $this->codigoMaterial = $codigoMaterial;
    }

    public static function fromArray(array $data): self
    {
        return new self($data['codigo_material']);
    }

    public function toArray(): array
    {
        return [
            'CodigoMaterial' => $this->codigoMaterial
        ];
    }
}
