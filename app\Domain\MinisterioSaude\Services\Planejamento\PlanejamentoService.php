<?php

namespace Domain\MinisterioSaude\Services\Planejamento;

use Domain\MinisterioSaude\Exceptions\CustomMessageException;
use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Services\Planejamento\Input\AtualizarPedidoInput;
use Domain\MinisterioSaude\Services\Planejamento\Input\ConsultarPedidosInput;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Carbon\Carbon;

class PlanejamentoService
{
    use HasServiceResponse;
    use HasLog;

    private $client;
    private $baseUrl;
    private $accessToken;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.items.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.items.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
        $this->accessToken = config('ministerio_saude.api.access_token');
    }

    /**
     * API 3.1 - Consultar Pedidos Farmanet
     *
     * Consulta pedidos no sistema GSNET do Ministério da Saúde
     *
     * @param ConsultarPedidosInput $input
     * @return ServiceResponse
     */
    public function consultarPedidos(ConsultarPedidosInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.planejamento.endpoints.consultar_pedidos');
            $url = "{$this->baseUrl}{$endpoint}";

            $this->logInfo('PlanejamentoService@consultarPedidos - Iniciando consulta de pedidos via API real', [
                'url' => $url,
                'params' => $input->toArray()
            ]);

            $payload = $input->toQueryParams($this->accessToken);
            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ],
                'query' => $payload
            ]);
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('PlanejamentoService@consultarPedidos - Resposta recebida do Ministério', [
                'status_code' => $response->getStatusCode(),
                'total_itens_retornados' => count($responseData['Data'] ?? []),
                'protocolo' => $responseData['Protocolo'] ?? null
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
            }

            return $this->successResponse([
                'result' => $responseData,
                'total_itens' => count($responseData['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ], 'Consulta de pedidos realizada com sucesso via API real do Ministério.');
        } catch (CustomMessageException $e) {
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();

            $this->logError('PlanejamentoService@consultarPedidos - Erro na requisição para o Ministério', $e, [
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $input
            ]);

            $response = $this->failureResponse($errorMessage)->setNullData();

        } catch (\Exception $e) {
            $this->logError('PlanejamentoService@consultarPedidos - Erro geral', $e, ['request_data' => $input]);

            $response = $this->failureResponse("Erro ao consultar pedidos: {$e->getMessage()}")
                ->setNullData()
                ->setErrorCode('INTERNAL_ERROR');
        }
        return $response;
    }

    /**
     * API 3.2 - Atualizar Pedido Farmanet
     *
     * Atualiza pedido no sistema GSNET do Ministério da Saúde
     *
     * @param AtualizarPedidoInput $input
     * @return ServiceResponse
     */
    public function atualizarPedido(AtualizarPedidoInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.planejamento.endpoints.atualizar_pedido');
            $url = "{$this->baseUrl}{$endpoint}";

            $this->logInfo('PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API', [
                'url' => $url,
                'params' => $input->toArray()
            ]);

            $payload = $input->toQueryParams($this->accessToken);
            $response = $this->client->put($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ],
                'query' => $payload
            ]);
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('PlanejamentoService@atualizarPedido - Resposta recebida do Ministério', [
                'status_code' => $response->getStatusCode()
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
            }

            return $this->successResponse([
                'result' => $responseData,
                'timestamp' => Carbon::now()->toISOString()
            ], 'Atualização de pedido realizada com sucesso via API real do Ministério.');
        } catch (CustomMessageException $e) {
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();

            $this->logError('PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério', $e, [
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $input
            ]);

            $response = $this->failureResponse($errorMessage)->setNullData();

        } catch (\Exception $e) {
            $this->logError('PlanejamentoService@atualizarPedido - Erro geral', $e, ['request_data' => $input]);

            $response = $this->failureResponse("Erro ao atualizar pedido: {$e->getMessage()}")
                ->setNullData()
                ->setErrorCode('INTERNAL_ERROR');
        }
        return $response;
    }
}
