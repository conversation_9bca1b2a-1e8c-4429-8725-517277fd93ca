<?php

namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;

use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
use Illuminate\Database\Eloquent\Collection;

class MinisterioSaudeLogRepository extends BaseAbastractRepository implements MinisterioSaudeLogRepositoryInterface
{
    protected $model;

    public function __construct(MinisterioSaudeLog $model)
    {
        parent::__construct($model);
    }

    public function list(array $filtros): Collection
    {
        return $this->model->get();
    }

    public function storeConsultaEndereco($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return new Collection($this->model->create([
            'operacao' => 'consultar_endereco',
            'endpoint' => '/Fatura/consultarEnderecoLocal',
            'metodo_http' => 'GET',
            'request_data' => $requestData,
            'response_data' => $responseData,
            'status_code' => $responseData['status_code'] ?? 0,
            'request_token' => $responseData['RequestToken'] ?? null,
            'result_code' => $responseData['ResultCode'] ?? null,
            'message' => $responseData['Message'] ?? null,
            'dt_start' => $responseData['DtStart'] ?? null,
            'dt_end' => $responseData['DtEnd'] ?? null,
            'sucesso' => $sucesso,
            'erro_message' => $erro,
            'ip_address' => request()->ip()
        ]));
    }
}
