<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Services\Item\Input\ConsultarItensInput;
use Domain\MinisterioSaude\Traits\HasLog;
use Illuminate\Http\JsonResponse;
use App\Controllers\Controller;
use Illuminate\Support\Facades\Log;
// Requests
use Domain\MinisterioSaude\Requests\Item\ConsultarItensRequest;
// Services
use Domain\MinisterioSaude\Services\Item\ConsultarItensService;

class ItemController extends Controller
{
    use HasLog;

    private ConsultarItensService $consultarItensService;

    public function __construct(ConsultarItensService $consultarItensService)
    {
        $this->consultarItensService = $consultarItensService;
    }

    /**
     * API 1.4 - Consultar Itens do Sistema GSNET
     *
     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
     *
     * @param ConsultarItensRequest $request
     * @return JsonResponse
     */
    public function consultarItens(ConsultarItensRequest $request): JsonResponse
    {
        try {
            $result = $this->consultarItensService->consultarItens(ConsultarItensInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('ItemController@consultarItens - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }
}
