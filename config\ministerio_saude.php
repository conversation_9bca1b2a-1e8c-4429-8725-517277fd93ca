<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Minist<PERSON><PERSON> da Saúde SP Configuration
    |--------------------------------------------------------------------------
    |
    | Configurações para integração com a API do Ministério da Saúde SP
    |
    */

    'api' => [
        'base_url' => [
            'production' => env('MS_SP_API_BASE_URL_PROD', 'https://operadorgsnethml.saude.sp.gov.br'),
            'homolog' => env('MS_SP_API_BASE_URL_HOMOLOG', 'https://operadorgsnethml.saude.sp.gov.br'),
        ],
        'system_code' => env('MS_SP_API_SYSTEM_CODE', ''),
        'environment' => env('MS_SP_API_ENVIRONMENT', 'homolog'), // 'production' ou 'homolog'
        'timeout' => env('MS_SP_API_TIMEOUT', 30),
        'force_external_in_dev' => env('MS_SP_API_FORCE_EXTERNAL_DEV', true), // Forçar uso de APIs reais
        'access_token' => env('MS_SP_API_ACCESS_TOKEN', ''),
    ],

    'database' => [
        'connection' => 'ministerio_saude_sp',
        'host' => env('DB_HOST_MS_SP', '*************'),
        'database' => env('DB_DATABASE_MS_SP', 'db_ms_sp'),
    ],

    'farmanet' => [
        'base_url' => [
            'production' => env('MS_SP_FARMANET_BASE_URL_PROD', 'https://operadorgsnethml.saude.sp.gov.br'),
            'homolog' => env('MS_SP_FARMANET_BASE_URL_HOMOLOG', 'https://operadorgsnethml.saude.sp.gov.br'),
        ],
        'endpoints' => [
            // APIs de Status
            'inserir_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/inserirStatusOp',
            'consultar_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp',

            // APIs de Endereço
            'consultar_endereco_local' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal',

            // APIs de Fatura GSNET
            'consultar_faturas_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar',
            'atualizar_status_fatura_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/atualizar',

            // APIs de Itens
            'consultar_itens' => '/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens',

            // APIs Farmanet - Pedidos
            'consultar_pedidos' => '/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos',
            'atualizar_pedido' => '/AtualizarPedidoOperador',

            // APIs Farmanet - Faturas (URL corrigida)
            'consultar_faturas' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura',
            'confirmar_recebimento_fatura' => '/ConfirmarRecebimentoFatura',

            // APIs Farmanet - Preços
            'obter_preco_medio' => '/ObterPrecoMedioItens',
        ],
        'timeout' => env('MS_SP_FARMANET_TIMEOUT', 60),
        'environment' => env('MS_SP_FARMANET_ENVIRONMENT', 'homolog'),
        'force_real_api' => env('MS_SP_FARMANET_FORCE_REAL', true), // Sempre usar APIs reais
    ],

    'validation' => [
        'max_length' => [
            'id_origem' => 11,
            'nome_status' => 40,
            'descricao_status' => 240,
            'access_token' => 240,
        ]
    ],

    'items' => [
        'base_url' => [
            'production' => env('MS_SP_ITEMS_BASE_URL_PROD', 'https://operadorgsnethml.saude.sp.gov.br'),
            'homolog' => env('MS_SP_ITEMS_BASE_URL_HOMOLOG', 'https://operadorgsnethml.saude.sp.gov.br'),
        ],
        'endpoints' => [
            'consultar_itens' => '/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens',
        ],
        'timeout' => env('MS_SP_ITEMS_TIMEOUT', 60),
        'environment' => env('MS_SP_ITEMS_ENVIRONMENT', 'homolog'),
        'force_real_api' => env('MS_SP_ITEMS_FORCE_REAL', true), // Sempre usar APIs reais
    ],

    'planejamento' => [
        'endpoints' => [
            'consultar_pedidos' => '/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos',
            'atualizar_pedido' => '/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos',
        ],
    ],

    'sync' => [
        'enabled' => env('MS_SP_SYNC_ENABLED', true),
        'auto_sync_interval' => env('MS_SP_AUTO_SYNC_INTERVAL', 3600), // segundos
    ],

    'geolocalizacao' => [
        'enabled' => env('MS_SP_GEO_ENABLED', true),
        'endereco_base' => env('MS_SP_ENDERECO_BASE', 'Rua Manoel Borba Gato, 100, São Paulo, SP'),
        'coordenadas_base' => [
            'latitude' => env('MS_SP_BASE_LAT', -23.5889),
            'longitude' => env('MS_SP_BASE_LON', -46.6418),
        ],
        'api_timeout' => env('MS_SP_GEO_TIMEOUT', 30),
        'cache_duration' => env('MS_SP_GEO_CACHE', 86400), // 24 horas
        'batch_limit' => env('MS_SP_GEO_BATCH_LIMIT', 50),
        'api_delay_ms' => env('MS_SP_GEO_DELAY', 500), // delay entre requisições
    ]
];
