[2025-09-16 21:00:24] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:00:25] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:00:48] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:00:48] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:01:10] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:01:10] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:13:10] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:24:09] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:24:10] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:35:52] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:35:52] homolog.ERROR: EnderecoLocalService - Erro na API externa {"error":"Erro na comunicação com API do Ministério: Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&IdLocal=1011&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","url":"https://operadorgsnethml.saude.sp.gov.br"} 
[2025-09-16 21:47:12] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 21:47:13] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:03:06] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens?AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","total_itens":4,"codigos_materiais":[1560,785,3500,2560]} 
[2025-09-16 22:03:15] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":4,"protocolo":"1F677D41-62C6-40CB-98A4-1DD947B33DD8"} 
[2025-09-16 22:15:37] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:01] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:17] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:26] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\StatusOperadorRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:16:50] homolog.ERROR: Target [Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface] is not instantiable while building [Domain\MinisterioSaude\Controllers\FaturaFarmanetController, Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService]. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target [Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\Contracts\\MinisterioSaudeLogRepositoryInterface] is not instantiable while building [Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController, Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService]. at C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1089)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(886): Illuminate\\Container\\Container->notInstantiable('Domain\\\\Minister...')
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1027): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(947): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->resolveDependencies(Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#48 {main}
"} 
[2025-09-16 22:17:44] homolog.ERROR: Access level to Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::$model must be protected (as in class Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository) or weaker {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::$model must be protected (as in class Domain\\MinisterioSaude\\Repositories\\Common\\BaseAbastractRepository) or weaker at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:10)
[stacktrace]
#0 {main}
"} 
[2025-09-16 22:17:58] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:18:05] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:18:06] homolog.ERROR: Return value of Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\Database\Eloquent\Collection, instance of Domain\MinisterioSaude\Models\MinisterioSaudeLog returned {"exception":"[object] (TypeError(code: 0): Return value of Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\\Database\\Eloquent\\Collection, instance of Domain\\MinisterioSaude\\Models\\MinisterioSaudeLog returned at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:40)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService.php(99): Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository->storeConsultaEndereco(Array, Array, true)
#1 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(93): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService->consultarEndereco(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\Input\\ConsultaEnderecoInput))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarEndereco(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarEnderecoRequest))
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarEndere...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarEndere...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#36 {main}
"} 
[2025-09-16 22:19:31] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:19:34] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 22:19:38] homolog.ERROR: Return value of Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\Database\Eloquent\Collection, instance of Domain\MinisterioSaude\Models\MinisterioSaudeLog returned {"exception":"[object] (TypeError(code: 0): Return value of Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository::storeConsultaEndereco() must be an instance of Illuminate\\Database\\Eloquent\\Collection, instance of Domain\\MinisterioSaude\\Models\\MinisterioSaudeLog returned at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository.php:40)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService.php(99): Domain\\MinisterioSaude\\Repositories\\FaturaFarmanet\\MinisterioSaudeLogRepository->storeConsultaEndereco(Array, Array, true)
#1 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(93): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\EnderecoLocalService->consultarEndereco(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\EnderecoLocal\\Input\\ConsultaEnderecoInput))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarEndereco(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarEnderecoRequest))
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarEndere...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarEndere...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#36 {main}
"} 
[2025-09-16 22:19:39] homolog.INFO: EnderecoLocalService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal","params":{"IdGestor":"11","IdLocal":"1011"}} 
[2025-09-16 22:19:44] homolog.INFO: EnderecoLocalService - Resposta da API real recebida {"status_code":200,"endereco_encontrado":true} 
[2025-09-16 23:02:30] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-16 23:02:31] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":0,"protocolo":"89B83395-CED6-4791-94D1-03F94D2F4ABD"} 
[2025-09-16 23:10:36] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-16 23:10:45] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-16 23:10:46] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":4,"protocolo":"EE4EB18D-82AC-4D9B-81CC-4ABBF49BC594"} 
[2025-09-16 23:23:51] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-16 23:23:52] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":4,"protocolo":"CE9C8C95-AFC2-4539-9585-95BBF6AFDDB4"} 
[2025-09-16 23:23:57] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(50): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:23:57] homolog.ERROR: Cannot use object of type Domain\MinisterioSaude\Helpers\ServiceResponse as array {"exception":"[object] (Error(code: 0): Cannot use object of type Domain\\MinisterioSaude\\Helpers\\ServiceResponse as array at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php:52)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#34 {main}
"} 
[2025-09-16 23:26:40] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:27:06] homolog.INFO: StatusOperadorService - Chamando API real do Ministério {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/inserirStatusOp","data":{"Data":{"IdOrigem":"INS036","NomeStatus":"INS","DescricaoStatus":"Teste de inserção via Insomnia"},"SystemCode":""}} 
[2025-09-16 23:27:07] homolog.INFO: StatusOperadorService - Resposta da API real recebida {"status_code":200,"response":{"Data":null,"RequestToken":"1b6d3bcb-38ee-4bdc-96ae-9e652477b6cc","ResultCode":0,"Message":"O status já consta na base de dados","DtStart":"2025-09-16T23:27:06.8219624-03:00","DtStartFmt":"16/09/2025 11:27:06 PM","DtEnd":"2025-09-16T23:27:07.0870871-03:00","DtEndFmt":"16/09/2025 11:27:07 PM","ValidationSummary":null}} 
[2025-09-16 23:27:07] homolog.INFO: StatusOperadorService - Status criado com sucesso {"status_id":52,"id_origem":"INS036","nome_status":"INS","api_externa_usada":true} 
[2025-09-16 23:27:42] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:28:42] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:30:22] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:30:30] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-16 23:30:41] homolog.ERROR: StatusOperadorService - Erro ao criar status {"error":"Id de Origem e Nome do Status já constam na base","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(49): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\StatusOperadorService->criarStatus(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\StatusOperador\\Input\\CriarStatusInput))
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->inserirStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\InserirStatusFaturaRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('inserirStatusFa...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'inserirStatusFa...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}"} 
[2025-09-17 01:03:59] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br?AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&IdGestor=11&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5","params":{"IdGestor":"11","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02","AnoReferencia":2024,"MesReferencia":9,"CodigoPrograma":5}} 
[2025-09-17 01:04:01] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"message":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br?AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&IdGestor=11&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","status_code":403,"response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>403 - Forbidden: Access is denied.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>403 - Forbidden: Access is denied.</h2>

  <h3>You do not have permission to view this directory or page using the credentials that you supplied.</h3>

 </fieldset></div>

</div>

</body>

</html>

","request_data":{"id_gestor":"11","ano_referencia":2024,"mes_referencia":9,"codigo_programa":5,"access_token":"7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","data_inicio":"2025-08-10","data_fim":"2025-09-02"}} 
[2025-09-17 01:25:43] homolog.ERROR: Argument 1 passed to Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\FaturaGsnetService::consultarFaturas() must be an instance of Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput, array given, called in C:\Dev\IBL\DocsAPI\app\Domain\MinisterioSaude\Controllers\FaturaFarmanetController.php on line 103 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService::consultarFaturas() must be an instance of Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput, array given, called in C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php on line 103 at C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php:45)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(103): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Array)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#35 {main}
"} 
[2025-09-17 01:26:31] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:26:31] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"status_code":403,"response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>403 - Forbidden: Access is denied.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>403 - Forbidden: Access is denied.</h2>

  <h3>You do not have permission to view this directory or page using the credentials that you supplied.</h3>

 </fieldset></div>

</div>

</body>

</html>

","request_data":{"id_gestor":"11","ano_referencia":"2024","mes_referencia":"9","codigo_programa":"5","data_inicio":"2025-08-10","data_fim":"2025-09-02","local_origem_id":null,"local_destino_id":null,"status":null,"page":null,"per_page":null},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(59): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(104): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:27:01] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:27:01] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"status_code":403,"response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>403 - Forbidden: Access is denied.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>403 - Forbidden: Access is denied.</h2>

  <h3>You do not have permission to view this directory or page using the credentials that you supplied.</h3>

 </fieldset></div>

</div>

</body>

</html>

","request_data":{"id_gestor":"11","ano_referencia":"2024","mes_referencia":"9","codigo_programa":"5","data_inicio":"2025-08-10","data_fim":"2025-09-02","local_origem_id":null,"local_destino_id":null,"status":null,"page":null,"per_page":null},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(59): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(104): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:27:36] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:27:37] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:27:49] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:27:49] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:27:54] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:27:54] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:27:56] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:27:57] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:28:11] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5"}} 
[2025-09-17 01:28:11] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"status_code":404,"response":"{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5'.\"}","request_data":{"id_gestor":"11","ano_referencia":"2024","mes_referencia":"9","codigo_programa":"5","data_inicio":null,"data_fim":null,"local_origem_id":null,"local_destino_id":null,"status":null,"page":null,"per_page":null},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(59): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(104): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:28:20] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:28:21] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:28:30] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5"}} 
[2025-09-17 01:28:30] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"status_code":404,"response":"{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5'.\"}","request_data":{"id_gestor":"11","ano_referencia":"2024","mes_referencia":"9","codigo_programa":"5","data_inicio":null,"data_fim":null,"local_origem_id":null,"local_destino_id":null,"status":null,"page":null,"per_page":null},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(59): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(104): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:29:40] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:29:41] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:29:58] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:29:58] homolog.ERROR: FaturaGsnetService - Erro na requisição para o Ministério {"status_code":404,"response":"{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissaoFim=2025-09-02'.\"}","request_data":{"id_gestor":"11","ano_referencia":"2024","mes_referencia":"9","codigo_programa":"5","data_inicio":null,"data_fim":"2025-09-02","local_origem_id":null,"local_destino_id":null,"status":null,"page":null,"per_page":null},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissaoFim=2025-09-02` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(59): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(104): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->consultarFaturas(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\ConsultaFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->consultarFaturas(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\ConsultarFaturasRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarFatura...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'consultarFatura...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:30:03] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:30:03] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:31:02] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:31:03] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:31:11] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:31:12] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:31:26] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:31:27] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:31:37] homolog.INFO: FaturaGsnetService - Consultando faturas via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar?IdGestor=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b&AnoReferencia=2024&MesReferencia=9&CodigoPrograma=5&DtEmissao=2025-08-10&DtEmissaoFim=2025-09-02","params":{"IdGestor":"11","AnoReferencia":"2024","MesReferencia":"9","CodigoPrograma":"5","DtEmissao":"2025-08-10","DtEmissaoFim":"2025-09-02"}} 
[2025-09-17 01:31:37] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":204,"message":"Ocorreu um erro durante o processo!","total_faturas":0} 
[2025-09-17 01:33:53] homolog.INFO: ConsultarItensService - Iniciando consulta de itens via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens","total_itens":4,"codigos_materiais":[]} 
[2025-09-17 01:33:59] homolog.INFO: ConsultarItensService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":4,"protocolo":"48FB205B-8EB4-41B3-B822-959B76C88FE5"} 
[2025-09-17 01:46:54] homolog.ERROR: Target class [Domain\MinisterioSaude\Requests\FaturaFarmamet\AtualizarStatusFaturaRequest] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\AtualizarStatusFaturaRequest] does not exist. at C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(79): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(48): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(28): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(41): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#40 {main}

[previous exception] [object] (ReflectionException(code: -1): Class Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\AtualizarStatusFaturaRequest does not exist at C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('Domain\\\\Minister...')
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('Domain\\\\Minister...')
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('Domain\\\\Minister...', Array, true)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('Domain\\\\Minister...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('Domain\\\\Minister...', Array)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(79): Illuminate\\Foundation\\Application->make('Domain\\\\Minister...')
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(48): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(28): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(41): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#41 {main}
"} 
[2025-09-17 01:50:28] homolog.ERROR: FaturaFarmanetController@atualizarStatusFatura - Erro {"error":"Undefined index: justificativa","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\AtualizarStatusFaturaInput.php(31): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8, 'Undefined index...', 'C:\\\\Dev\\\\IBL\\\\Docs...', 31, Array)
#1 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(121): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\AtualizarStatusFaturaInput::fromArray(Array)
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->atualizarStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\AtualizarStatusFaturaRequest))
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarStatus...', Array)
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#36 {main}"} 
[2025-09-17 01:50:49] homolog.INFO: FaturaGsnetService - Atualizando status da fatura via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/AtualizarStatus?protocoloIdGsnet=1&nrDocumento=1&idOrigem=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","params":{"protocoloIdGsnet":"1","nrDocumento":"1","idOrigem":11,"justificativa":null}} 
[2025-09-17 01:50:50] homolog.ERROR: FaturaGsnetService - Erro na atualização do status da fatura {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/AtualizarStatus?protocoloIdGsnet=1&nrDocumento=1&idOrigem=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","data":{"protocoloIdGsnet":"1","nrDocumento":"1","idOrigem":11,"justificativa":null,"AccessToken":"7fe9ba49-6ce4-4773-b7a6-f6e46a12565b"},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/AtualizarStatus?protocoloIdGsnet=1&nrDocumento=1&idOrigem=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b` resulted in a `404 Not Found` response:
<!DOCTYPE html>

<html>

    <head>

        <title>The resource cannot be found.</title>

        <meta name=\"viewport\" (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService.php(116): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController.php(121): Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\FaturaGsnetService->atualizarStatusFatura(Object(Domain\\MinisterioSaude\\Services\\FaturaFarmanet\\Fatura\\Input\\AtualizarStatusFaturaInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController->atualizarStatusFatura(Object(Domain\\MinisterioSaude\\Requests\\FaturaFarmamet\\AtualizarStatusFaturaRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarStatus...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\FaturaFarmanetController), 'atualizarStatus...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 01:51:07] homolog.INFO: FaturaGsnetService - Atualizando status da fatura via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/atualizar?protocoloIdGsnet=1&nrDocumento=1&idOrigem=11&AccessToken=7fe9ba49-6ce4-4773-b7a6-f6e46a12565b","params":{"protocoloIdGsnet":"1","nrDocumento":"1","idOrigem":11,"justificativa":null}} 
[2025-09-17 01:51:08] homolog.INFO: FaturaGsnetService - Resposta da API real recebida {"status_code":200,"result_code":0,"message":"Erro ao atualizar Fatura"} 
[2025-09-17 02:06:51] homolog.INFO: PlanejamentoService - Iniciando consulta de pedidos via API real {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"codigo_programa":null,"ano_referencia":2025,"mes_referencia":null,"ano_periodo_referencia":null,"id_gestor":11}} 
[2025-09-17 02:06:51] homolog.ERROR: PlanejamentoService - Erro na requisição para o Ministério {"status_code":403,"response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>403 - Forbidden: Access is denied.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>403 - Forbidden: Access is denied.</h2>

  <h3>You do not have permission to view this directory or page using the credentials that you supplied.</h3>

 </fieldset></div>

</div>

</body>

</html>

","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\ConsultarPedidosInput":{"codigoPrograma":null,"anoReferencia":2025,"mesReferencia":null,"anoPeriodoReferencia":null,"idGestor":11}},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br` resulted in a `403 Forbidden` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(54): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(42): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->consultarPedidos(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\ConsultarPedidosInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->ConsultarPedidos(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\ConsultarPedidosRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarPedido...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'consultarPedido...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:07:30] homolog.INFO: PlanejamentoService - Iniciando consulta de pedidos via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos","params":{"codigo_programa":null,"ano_referencia":2025,"mes_referencia":null,"ano_periodo_referencia":null,"id_gestor":11}} 
[2025-09-17 02:07:30] homolog.ERROR: PlanejamentoService - Erro na requisição para o Ministério {"status_code":404,"response":"{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos'.\"}","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\ConsultarPedidosInput":{"codigoPrograma":null,"anoReferencia":2025,"mesReferencia":null,"anoPeriodoReferencia":null,"idGestor":11}},"error":"Client error: `GET https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(54): GuzzleHttp\\Client->get('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(42): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->consultarPedidos(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\ConsultarPedidosInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->ConsultarPedidos(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\ConsultarPedidosRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('consultarPedido...', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'consultarPedido...')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:08:46] homolog.INFO: PlanejamentoService - Iniciando consulta de pedidos via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos","params":{"codigo_programa":null,"ano_referencia":2025,"mes_referencia":null,"ano_periodo_referencia":null,"id_gestor":11}} 
[2025-09-17 02:08:52] homolog.INFO: PlanejamentoService - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":362,"protocolo":null} 
[2025-09-17 02:10:00] homolog.INFO: PlanejamentoService@consultarPedidos - Iniciando consulta de pedidos via API real {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos","params":{"codigo_programa":null,"ano_referencia":2025,"mes_referencia":null,"ano_periodo_referencia":null,"id_gestor":11}} 
[2025-09-17 02:10:05] homolog.INFO: PlanejamentoService@consultarPedidos - Resposta recebida do Ministério {"status_code":200,"total_itens_retornados":362,"protocolo":null} 
[2025-09-17 02:21:04] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:21:04] homolog.ERROR: PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério {"status_code":405,"response":"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xmlns=\"http://www.w3.org/1999/xhtml\">

<head>

<meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"/>

<title>405 - HTTP verb used to access this page is not allowed.</title>

<style type=\"text/css\">

<!--

body{margin:0;font-size:.7em;font-family:Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;}

fieldset{padding:0 15px 10px 15px;} 

h1{font-size:2.4em;margin:0;color:#FFF;}

h2{font-size:1.7em;margin:0;color:#CC0000;} 

h3{font-size:1.2em;margin:10px 0 0 0;color:#000000;} 

#header{width:96%;margin:0 0 0 0;padding:6px 2% 6px 2%;font-family:\"trebuchet MS\", Verdana, sans-serif;color:#FFF;

background-color:#555555;}

#content{margin:0 0 0 2%;position:relative;}

.content-container{background:#FFF;width:96%;margin-top:8px;padding:10px;position:relative;}

-->

</style>

</head>

<body>

<div id=\"header\"><h1>Server Error</h1></div>

<div id=\"content\">

 <div class=\"content-container\"><fieldset>

  <h2>405 - HTTP verb used to access this page is not allowed.</h2>

  <h3>The page you are looking for cannot be displayed because an invalid method (HTTP verb) was used to attempt access.</h3>

 </fieldset></div>

</div>

</body>

</html>

","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput":{"idProgramaSaude":5,"codigoPedido":4075600,"protocoloOperador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}},"error":"Client error: `PUT https://operadorgsnethml.saude.sp.gov.br` resulted in a `405 Method Not Allowed` response:
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">

<html xml (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(78): GuzzleHttp\\Client->request('PUT', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(128): GuzzleHttp\\Client->put('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(60): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->atualizarPedido(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->atualizarPedido(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\AtualizarPedidoRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarPedido', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'atualizarPedido')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:21:24] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:21:24] homolog.ERROR: PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério {"status_code":404,"response":"<!DOCTYPE html>

<html>

    <head>

        <title>The resource cannot be found.</title>

        <meta name=\"viewport\" content=\"width=device-width\" />

        <style>

         body {font-family:\"Verdana\";font-weight:normal;font-size: .7em;color:black;} 

         p {font-family:\"Verdana\";font-weight:normal;color:black;margin-top: -5px}

         b {font-family:\"Verdana\";font-weight:bold;color:black;margin-top: -5px}

         H1 { font-family:\"Verdana\";font-weight:normal;font-size:18pt;color:red }

         H2 { font-family:\"Verdana\";font-weight:normal;font-size:14pt;color:maroon }

         pre {font-family:\"Consolas\",\"Lucida Console\",Monospace;font-size:11pt;margin:0;padding:0.5em;line-height:14pt}

         .marker {font-weight: bold; color: black;text-decoration: none;}

         .version {color: gray;}

         .error {margin-bottom: 10px;}

         .expandable { text-decoration:underline; font-weight:bold; color:navy; cursor:pointer; }

         @media screen and (max-width: 639px) {

          pre { width: 440px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }

         }

         @media screen and (max-width: 479px) {

          pre { width: 280px; }

         }

        </style>

    </head>



    <body bgcolor=\"white\">



            <span><H1>Server Error in '/Prodesp.Gsnet.Operador.planejamento.servico' Application.<hr width=100% size=1 color=silver></H1>



            <h2> <i>The resource cannot be found.</i> </h2></span>



            <font face=\"Arial, Helvetica, Geneva, SunSans-Regular, sans-serif \">



            <b> Description: </b>HTTP 404. The resource you are looking for (or one of its dependencies) could have been removed, had its name changed, or is temporarily unavailable. &nbsp;Please review the following URL and make sure that it is spelled correctly.

            <br><br>



            <b> Requested URL: </b>/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador<br><br>



            </font>



    </body>

</html>

","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput":{"idProgramaSaude":5,"codigoPedido":4075600,"protocoloOperador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}},"error":"Client error: `PUT https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador` resulted in a `404 Not Found` response:
<!DOCTYPE html>

<html>

    <head>

        <title>The resource cannot be found.</title>

        <meta name=\"viewport\" (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(78): GuzzleHttp\\Client->request('PUT', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(128): GuzzleHttp\\Client->put('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(60): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->atualizarPedido(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->atualizarPedido(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\AtualizarPedidoRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarPedido', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'atualizarPedido')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:21:51] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:21:52] homolog.ERROR: PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério {"status_code":404,"response":"<!DOCTYPE html>

<html>

    <head>

        <title>The resource cannot be found.</title>

        <meta name=\"viewport\" content=\"width=device-width\" />

        <style>

         body {font-family:\"Verdana\";font-weight:normal;font-size: .7em;color:black;} 

         p {font-family:\"Verdana\";font-weight:normal;color:black;margin-top: -5px}

         b {font-family:\"Verdana\";font-weight:bold;color:black;margin-top: -5px}

         H1 { font-family:\"Verdana\";font-weight:normal;font-size:18pt;color:red }

         H2 { font-family:\"Verdana\";font-weight:normal;font-size:14pt;color:maroon }

         pre {font-family:\"Consolas\",\"Lucida Console\",Monospace;font-size:11pt;margin:0;padding:0.5em;line-height:14pt}

         .marker {font-weight: bold; color: black;text-decoration: none;}

         .version {color: gray;}

         .error {margin-bottom: 10px;}

         .expandable { text-decoration:underline; font-weight:bold; color:navy; cursor:pointer; }

         @media screen and (max-width: 639px) {

          pre { width: 440px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }

         }

         @media screen and (max-width: 479px) {

          pre { width: 280px; }

         }

        </style>

    </head>



    <body bgcolor=\"white\">



            <span><H1>Server Error in '/Prodesp.Gsnet.Operador.planejamento.servico' Application.<hr width=100% size=1 color=silver></H1>



            <h2> <i>The resource cannot be found.</i> </h2></span>



            <font face=\"Arial, Helvetica, Geneva, SunSans-Regular, sans-serif \">



            <b> Description: </b>HTTP 404. The resource you are looking for (or one of its dependencies) could have been removed, had its name changed, or is temporarily unavailable. &nbsp;Please review the following URL and make sure that it is spelled correctly.

            <br><br>



            <b> Requested URL: </b>/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador<br><br>



            </font>



    </body>

</html>

","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput":{"idProgramaSaude":5,"codigoPedido":4075600,"protocoloOperador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}},"error":"Client error: `PUT https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidoOperador` resulted in a `404 Not Found` response:
<!DOCTYPE html>

<html>

    <head>

        <title>The resource cannot be found.</title>

        <meta name=\"viewport\" (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(78): GuzzleHttp\\Client->request('PUT', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(128): GuzzleHttp\\Client->put('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(60): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->atualizarPedido(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->atualizarPedido(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\AtualizarPedidoRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarPedido', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'atualizarPedido')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:22:05] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:22:05] homolog.ERROR: PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério {"status_code":404,"response":"{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos'.\"}","request_data":{"Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput":{"idProgramaSaude":5,"codigoPedido":4075600,"protocoloOperador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}},"error":"Client error: `PUT https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos` resulted in a `404 Not Found` response:
{\"Message\":\"No HTTP resource was found that matches the request URI 'https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gs (truncated...)
","trace":"#0 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Dev\\IBL\\DocsAPI\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(78): GuzzleHttp\\Client->request('PUT', 'https://operado...', Array)
#11 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService.php(128): GuzzleHttp\\Client->put('https://operado...', Array)
#12 C:\\Dev\\IBL\\DocsAPI\\app\\Domain\\MinisterioSaude\\Controllers\\PlanejamentoController.php(60): Domain\\MinisterioSaude\\Services\\Planejamento\\PlanejamentoService->atualizarPedido(Object(Domain\\MinisterioSaude\\Services\\Planejamento\\Input\\AtualizarPedidoInput))
#13 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Domain\\MinisterioSaude\\Controllers\\PlanejamentoController->atualizarPedido(Object(Domain\\MinisterioSaude\\Requests\\Planejamento\\AtualizarPedidoRequest))
#14 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('atualizarPedido', Array)
#15 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Domain\\MinisterioSaude\\Controllers\\PlanejamentoController), 'atualizarPedido')
#16 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\ValidateJWT.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\ValidateJWT->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Dev\\IBL\\DocsAPI\\app\\Support\\Middleware\\Cors.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Support\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Dev\\IBL\\DocsAPI\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Dev\\IBL\\DocsAPI\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Dev\\IBL\\DocsAPI\\server.php(21): require_once('C:\\\\Dev\\\\IBL\\\\Docs...')
#47 {main}"} 
[2025-09-17 02:22:21] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:22:36] homolog.INFO: PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API {"url":"https://operadorgsnethml.saude.sp.gov.br/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos","params":{"id_programa_saude":5,"codigo_pedido":4075600,"protocolo_operador":"b7fbc757-0fbb-4f0c-ab5d-e8028cd744ff"}} 
[2025-09-17 02:22:37] homolog.INFO: PlanejamentoService@atualizarPedido - Resposta recebida do Ministério {"status_code":200} 
